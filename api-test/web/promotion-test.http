### 测试1：根据促销ID列表查询（原有功能）
POST http://localhost:10041/sale/shopsale/promotion/getCloudOrderPromotionInfo
Accept: application/json
ngp-router: {"companyId":"1072154373372534784","profileId":"1072154373372534785","serverId":"20231114","employeeId":"1072154375708762112","btypeId":"0","greyFlag":false,"testFlag":false,"demoFlag":false,"vipFlag":false,"ycFlag":false,"dbReadOnlyFlag":false,"productId":66,"adminStatus":true,"deploy":"master","routes":null,"region":0,"formal":0,"aloneDeploy":"sale","gateway":"http://eshop.gateway.ngp.wsgjp.com.cn","aloneServer":"http://localhost:10041"}
Content-Type: application/json

{
  "promotionIds": [
    "*********",
    "*********"
  ]
}

### 测试2：根据销售机构ID + 商品ID数组查询（原有功能）
POST http://localhost:10041/sale/shopsale/promotion/getCloudOrderPromotionInfo
Accept: application/json
ngp-router: {"companyId":"1109914400061448192","profileId":"1109914400061448193","serverId":"20231114","employeeId":"1109914403647578112","btypeId":"0","greyFlag":false,"testFlag":false,"demoFlag":false,"vipFlag":false,"ycFlag":false,"dbReadOnlyFlag":false,"productId":66,"adminStatus":true,"deploy":"feature-5-9","routes":null,"region":0,"formal":1,"aloneDeploy":"sale","gateway":"http://eshop.gateway.ngp.wsgjp.com.cn","aloneServer":"http://localhost:10041"}
Content-Type: application/json

{
  "otypeId": "1594315147587902085",
  "ptypeIds": [
    "1621615110772513413"
  ]
}

### 测试3：根据往来单位ID + 销售机构ID + 商品ID数组查询（新增功能）
POST http://localhost:10041/sale/shopsale/promotion/getCloudOrderPromotionInfo
Accept: application/json
ngp-router: {"companyId":"1072154373372534784","profileId":"1072154373372534785","serverId":"20231114","employeeId":"1072154375708762112","btypeId":"0","greyFlag":false,"testFlag":false,"demoFlag":false,"vipFlag":false,"ycFlag":false,"dbReadOnlyFlag":false,"productId":66,"adminStatus":true,"deploy":"master","routes":null,"region":0,"formal":0,"aloneDeploy":"sale","gateway":"http://eshop.gateway.ngp.wsgjp.com.cn","aloneServer":"http://localhost:10041"}
Content-Type: application/json

{
  "btypeId": "1607485269664140671",
  "otypeId": "1211282602556696925",
  "ptypeIds": [
    "1663201750091304319"
  ]
}

### 测试4：根据销售机构ID查询（ptypeIds可选）
POST http://localhost:10041/sale/shopsale/promotion/getCloudOrderPromotionInfo
Accept: application/json
ngp-router: {"companyId":"1109914400061448192","profileId":"1109914400061448193","serverId":"20231114","employeeId":"1109914403647578112","btypeId":"0","greyFlag":false,"testFlag":false,"demoFlag":false,"vipFlag":false,"ycFlag":false,"dbReadOnlyFlag":false,"productId":66,"adminStatus":true,"deploy":"feature-5-9","routes":null,"region":0,"formal":1,"aloneDeploy":"sale","gateway":"http://eshop.gateway.ngp.wsgjp.com.cn","aloneServer":"http://localhost:10041"}
Content-Type: application/json

{
  "otypeId": "1594315147587902085"
}

### 测试5：根据往来单位ID + 销售机构ID查询（ptypeIds可选）
POST http://localhost:10041/sale/shopsale/promotion/getCloudOrderPromotionInfo
Accept: application/json
ngp-router: {"aloneDeploy":"sale","profileId":"800435028449300481","employeeId":"800435030353514496","btypeId":"0","serverId":"20221209","gateway":"http://eshop.gateway.ngp.wsgjp.com.cn","aloneServer":"http://localhost:10041","deploy":"master","productId":66,"adminStatus":true,"routes":null}
Content-Type: application/json

{
  "btypeId": "*********",
  "otypeId": "*********"
}

### 测试6：边界测试 - 只传入往来单位ID（应返回空列表）
POST http://localhost:10041/sale/shopsale/promotion/getCloudOrderPromotionInfo
Accept: application/json
ngp-router: {"companyId":"1109914400061448192","profileId":"1109914400061448193","serverId":"20231114","employeeId":"1109914403647578112","btypeId":"0","greyFlag":false,"testFlag":false,"demoFlag":false,"vipFlag":false,"ycFlag":false,"dbReadOnlyFlag":false,"productId":66,"adminStatus":true,"deploy":"feature-5-9","routes":null,"region":0,"formal":1,"aloneDeploy":"sale","gateway":"http://eshop.gateway.ngp.wsgjp.com.cn","aloneServer":"http://localhost:10041"}
Content-Type: application/json

{
  "btypeId": "*********"
}

### 测试5：边界测试 - 空参数（应返回空列表）
POST http://localhost:10041/sale/shopsale/promotion/getCloudOrderPromotionInfo
Accept: application/json
ngp-router: {"companyId":"1109914400061448192","profileId":"1109914400061448193","serverId":"20231114","employeeId":"1109914403647578112","btypeId":"0","greyFlag":false,"testFlag":false,"demoFlag":false,"vipFlag":false,"ycFlag":false,"dbReadOnlyFlag":false,"productId":66,"adminStatus":true,"deploy":"feature-5-9","routes":null,"region":0,"formal":1,"aloneDeploy":"sale","gateway":"http://eshop.gateway.ngp.wsgjp.com.cn","aloneServer":"http://localhost:10041"}
Content-Type: application/json

{
}

### 测试6：getPromotionListByBtypeId 接口测试（原有接口）
POST http://localhost:10041/sale/shopsale/promotionExecute/getPromotionListByBtypeId
Accept: application/json
ngp-router: {"companyId":"1109914400061448192","profileId":"1109914400061448193","serverId":"20231114","employeeId":"1109914403647578112","btypeId":"0","greyFlag":false,"testFlag":false,"demoFlag":false,"vipFlag":false,"ycFlag":false,"dbReadOnlyFlag":false,"productId":66,"adminStatus":true,"deploy":"feature-5-9","routes":null,"region":0,"formal":1,"aloneDeploy":"sale","gateway":"http://eshop.gateway.ngp.wsgjp.com.cn","aloneServer":"http://localhost:10041"}
Content-Type: application/json

{
  "btypeId": "*********",
  "otypeId": "*********"
}

### 测试7：getPromotionListByBtypeId 接口测试 - 新增商品过滤功能
POST http://localhost:10041/sale/shopsale/promotionExecute/getPromotionListByBtypeId
Accept: application/json
ngp-router: {"companyId":"1109914400061448192","profileId":"1109914400061448193","serverId":"20231114","employeeId":"1109914403647578112","btypeId":"0","greyFlag":false,"testFlag":false,"demoFlag":false,"vipFlag":false,"ycFlag":false,"dbReadOnlyFlag":false,"productId":66,"adminStatus":true,"deploy":"feature-5-9","routes":null,"region":0,"formal":1,"aloneDeploy":"sale","gateway":"http://eshop.gateway.ngp.wsgjp.com.cn","aloneServer":"http://localhost:10041"}
Content-Type: application/json

{
  "btypeId": "*********",
  "otypeId": "*********",
  "ptypeIds": [
    "*********",
    "*********"
  ]
}

### 测试8：验证全部促销查询逻辑 - 查询销售机构的所有促销（包括全部促销）
POST http://localhost:10041/sale/shopsale/promotion/getCloudOrderPromotionInfo
Accept: application/json
ngp-router: {"companyId":"1109914400061448192","profileId":"1109914400061448193","serverId":"20231114","employeeId":"1109914403647578112","btypeId":"0","greyFlag":false,"testFlag":false,"demoFlag":false,"vipFlag":false,"ycFlag":false,"dbReadOnlyFlag":false,"productId":66,"adminStatus":true,"deploy":"feature-5-9","routes":null,"region":0,"formal":1,"aloneDeploy":"sale","gateway":"http://eshop.gateway.ngp.wsgjp.com.cn","aloneServer":"http://localhost:10041"}
Content-Type: application/json

{
  "otypeId": "1594315147587902085"
}

### 测试9：getPromotionList 接口测试 - 验证新增的btypeId支持
POST http://localhost:10041/sale/shopsale/promotion/getPromotionList
Accept: application/json
ngp-router: {"companyId":"1109914400061448192","profileId":"1109914400061448193","serverId":"20231114","employeeId":"1109914403647578112","btypeId":"0","greyFlag":false,"testFlag":false,"demoFlag":false,"vipFlag":false,"ycFlag":false,"dbReadOnlyFlag":false,"productId":66,"adminStatus":true,"deploy":"feature-5-9","routes":null,"region":0,"formal":1,"aloneDeploy":"sale","gateway":"http://eshop.gateway.ngp.wsgjp.com.cn","aloneServer":"http://localhost:10041"}
Content-Type: application/json

{
  "btypeId": "*********",
  "otypeId": "*********",
  "ptypeIds": [
    "*********",
    "*********"
  ],
  "state": 1,
  "stoped": 0,
  "useRange": 1,
  "promotionType": -1
}

### 测试9：getPromotionList 接口测试 - 验证新增的btypeId支持
POST http://localhost:10041/sale/shopsale/promotionExecute/executePromotion
Accept: application/json
ngp-router: {"profileId":1072154373372534785,"profileHisId":null,"employeeId":1072154375708762112,"btypeId":1607485269664140671,"deploy":"master","serverId":"20231114","adminStatus":true,"productId":0,"clientIp":null,"ngpSessionId":null,"loginName":null,"grey":false,"vipFlag":false,"ycFlag":false,"dbReadOnlyFlag":false,"extendData":null,"testFlag":false,"demoFlag":false,"profileState":null,"domain":null,"routes":null,"extendProperties":null,"region":null,"formal":null}
Content-Type: application/json

{
  "profileId": "1072154373372534785",
  "btypeId": "1607485269664140671",
  "outDetail": [
    {
      "detailId": "1",
      "comboRowParId": null,
      "comboRowId": null,
      "comboId": "0",
      "ptypeId": "1575778000018935167",
      "pFullName": null,
      "pUserCode": null,
      "standard": null,
      "ptypeType": null,
      "pFullBarCode": null,
      "skuId": "1575778000019262847",
      "skuName": null,
      "skuBarcode": null,
      "propValueId1": null,
      "propValueName1": null,
      "propValueId2": null,
      "propValueName2": null,
      "propValueId3": null,
      "propValueName3": null,
      "propValueId4": null,
      "propValueName4": null,
      "propValueId5": null,
      "propValueName5": null,
      "propValueId6": null,
      "propValueName6": null,
      "forecastCostPrice": null,
      "forecastCostTotal": null,
      "costPrice": 0,
      "costTotal": 0,
      "qty": null,
      "unitQty": 11,
      "subQty": 0,
      "currencyPrice": 24.000,
      "currencyTotal": 264.000,
      "discount": 1,
      "discountTotal": 264.000,
      "currencyDisedPrice": 24.000,
      "currencyDisedTotal": 24.000,
      "taxRate": 0.16000000,
      "currencyTaxTotal": null,
      "currencyDisedTaxedPrice": 24.000,
      "currencyDisedTaxedTotal": 264.000,
      "currencyFeeTotal": 0,
      "currencyPreferentialTotal": 0,
      "currencyCompletedPreferentialShare": null,
      "refundOrderPreferentialAllotTotal": 0,
      "picUrl": null,
      "batchNo": "",
      "produceDate": null,
      "expireDate": null,
      "position": null,
      "memo": null,
      "unitRate": 1,
      "unitId": "1575778000019131775",
      "unitName": null,
      "snenabled": null,
      "pcategory": null,
      "propenabled": null,
      "batchenabled": null,
      "gift": false,
      "inoutType": null,
      "sourceVchtype": null,
      "sourceDetailId": null,
      "sourceVchcode": null,
      "protectDays": null,
      "completedQty": null,
      "completedTotal": null,
      "overQty": null,
      "overTotal": null,
      "unOverQty": null,
      "unOverTotal": null,
      "skuPrice": null,
      "fullbarcode": null
    }
  ]
}


###
