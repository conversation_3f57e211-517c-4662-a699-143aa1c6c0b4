package com.wsgjp.ct.sale.web.eshoporder;

import bf.datasource.annotation.PageDataSource;
import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.eshoporder.api.ProductToolApi;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderToolConfig;
import com.wsgjp.ct.sale.biz.eshoporder.constant.NameConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.constant.StringConstant;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Prop;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProductOperateLogType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.CreateComboParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.CreatePtypeParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.RefreshPtypeParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuAttrRelation;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuPageData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.product.EshopUpdateProductMarkRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.product.QueryEshopSkuListRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.QueryStockSyncLogParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.ComboSaveResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.OperateType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.SupportStockSyncShopTypeResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.*;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopStockSyncLogger;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.ProductDownloadService;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.ProductManageService;
import com.wsgjp.ct.sale.biz.eshoporder.service.stock.StockBuildService;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.StockUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.enums.FeatureEnum;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.product.EshopProductModifyFeature;
import com.wsgjp.ct.sale.web.eshoporder.entity.CommonConst;
import com.wsgjp.ct.sale.web.eshoporder.entity.response.product.ProductManagePageInitData;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderConst.STOCK_SYNC_THREAD_NAME;

/**
 * <AUTHOR>
 */
@Api(tags = "平台商品管理")
@RequestMapping("${app.id}/eshoporder/product")
@RestController
public class ProductController {

    private final ProductManageService manageService;
    private final ProductDownloadService downloadService;
    private final EshopService eshopService;
    private final EshopOrderToolConfig toolConfig;
    private final ProductToolApi toolApi;
    private final StockBuildService stockSvc;

    public ProductController(ProductManageService manageService, ProductDownloadService downloadService, EshopService eshopService,
                             EshopOrderToolConfig toolConfig, ProductToolApi toolApi, StockBuildService stockSvc) {
        this.manageService = manageService;
        this.downloadService = downloadService;
        this.eshopService = eshopService;
        this.toolConfig = toolConfig;
        this.toolApi = toolApi;
        this.stockSvc = stockSvc;
    }


    @RequestMapping(value = "/getInitData/{mode}", method = RequestMethod.GET)
    @ApiOperation("网店商品管理-获取管理界面初始化数据")
    public ProductManagePageInitData getPageInitData(@PathVariable String mode) {
        QueryEShopParameter parameter = new QueryEShopParameter();
        String userData;
        if (mode.equals(CommonConst.PRODUCT_PAGE_USER_DATA_KEY)) {
            userData = GlobalConfig.getUserData(CommonConst.PRODUCT_PAGE_USER_DATA, "");
        } else {
            userData = GlobalConfig.getUserData(CommonConst.PRODUCT_PAGE_USER_DATA_FRO_STOCK, "");
            List<ShopType> supportStockSyncShopTypes = EshopFactoryManager.listSupportStockSyncShopTypes();
            if (CollectionUtils.isNotEmpty(supportStockSyncShopTypes)) {
                parameter.setShopTypes(supportStockSyncShopTypes.stream().map(ShopType::getCode).collect(Collectors.toList()));
            }
        }
        parameter.setQueryStop(false);
        ProductManagePageInitData initData = new ProductManagePageInitData();
        List<EshopInfo> eshopInfoList = eshopService.queryEshopList(parameter);
        initData.setEshopInfoList(eshopInfoList);
        initData.setSelectedEshopIds(buildSelectedByUserData(userData, eshopInfoList));
        initData.setProductOperateLogTypes(buildProductOperateLogTypes());
        initData.setSupportModifyPlatformXcodeShopTypes(EshopUtils.listFeatureSupportedShopTypes(EshopProductModifyFeature.class.getSimpleName()));
        initData.setSupportSyncToSonShopTypes(Arrays.asList(114, 116, 134, 164));
        return initData;
    }

    private List<OperateType> buildProductOperateLogTypes() {
        List<OperateType> operateTypes = new ArrayList<>();
        ProductOperateLogType[] operateLogTypes = ProductOperateLogType.values();
        for (ProductOperateLogType operateLogType : operateLogTypes) {
            if(!operateLogType.getNewVersionShowEnabled()){
                continue;
            }
            operateTypes.add(new OperateType(operateLogType.getCode(), operateLogType.getName()));
        }
        return operateTypes;
    }


    private List<BigInteger> buildSelectedByUserData(String userData, List<EshopInfo> eshopInfoList) {
        List<BigInteger> eshopIds = new ArrayList<>();
        if (StringUtils.isEmpty(userData)) {
            if (CollectionUtils.isNotEmpty(eshopInfoList) && eshopInfoList.get(0) != null) {
                eshopIds.add(eshopInfoList.get(0).getOtypeId());
            }
            return eshopIds;
        }
        String[] split = userData.split(StringConstant.COMMA);
        for (String val : split) {
            if (StringUtils.isEmpty(val)) {
                continue;
            }
            BigInteger eshopId = new BigInteger(val);
            eshopIds.add(eshopId);
        }
        return eshopIds;
    }

    @RequestMapping(value = "/skuList", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-查询SKU列表")
    public PageResponse<EshopProductSkuPageData> querySkuList(@RequestBody PageRequest<QueryEshopSkuListRequest> params) {
        return manageService.querySkuList(params, false);
    }

    @RequestMapping(value = "/dataItemsRefresh", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-部分刷新")
    public List<EshopProductSkuPageData> dataItemsRefresh(@RequestBody QueryEshopSkuListRequest request) {
        request.setOrderCase("");
        return manageService.querySkuList(request);
    }

    @PostMapping("/skuListCount")
    @ApiOperation("网店商品管理-查询SKU列表总数")
    public int querySkuListCount(@RequestBody PageRequest<QueryEshopSkuListRequest> query) {
        return manageService.querySkuListCount(query.getQueryParams());
    }

    @PostMapping("/skuUnRelationCount")
    @ApiOperation("网店商品管理-查询未对应总数")
    public int getStockSyncPageInfoCount(@RequestBody QueryEshopSkuListRequest query) {
        return manageService.querySkuListCount(query);
    }

    @PostMapping("/doRefresh")
    @ApiOperation("网店商品管理-开始刷新商品")
    public String doRefresh(@RequestBody RefreshPtypeParams params) {
        if (!downloadService.checkCanCrateNewTask(params)) {
            return params.getTaskId();
        }
        params.setNeedProcessLog(false);
        ThreadPool threadPool = ThreadPoolFactory.build(NameConstantEnum.PRODUCT_REFRESH.getName());
        threadPool.executeAsync(x -> {
            if (toolConfig.isRefreshPtypeByTool()) {
                //todo 验证一下工具是否正常
                toolApi.doRefresh(params);
            } else {
                downloadService.doRefresh(params);
            }
        }, NameConstantEnum.PRODUCT_REFRESH.getName());
        return params.getTaskId();
    }

    @PostMapping("/doSingleRefresh")
    @ApiOperation("网店商品管理-刷新单个商品")
    public void doSingleRefresh(@RequestBody RefreshPtypeParams params) {
        if (CollectionUtils.isEmpty(params.getNumids())) {
            return;
        }
        params.setTaskId(UId.newId().toString());
        downloadService.doRefresh(params);
    }

    @PageDataSource
    @PostMapping("/defaultRuleList")
    @ApiOperation("网店商品管理-库存同步默认规则列表")
    public PageResponse<StockSyncRule> queryDefaultRule(@RequestBody PageRequest<QueryStockRuleParameter> query) {
        QueryStockRuleParameter parameter = query.getQueryParams();
        CommonUtil.initLimited(parameter);
        parameter.setShopTypes(StockUtil.querySupportSyncStockShopTypes());
        PageDevice.initPage(query);
        return PageDevice.readPage(manageService.queryDefaultRule(parameter));
    }

    @RequestMapping(value = "/generateProductXcode", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-生成网店商家编码")
    public String generateProductXcode(@RequestBody EshopGenerateProductXcodeRequest request) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        manageService.generateProductXcode(request, processLogger);
        return taskId;
    }

    @RequestMapping(value = "/modifyPlatformXcode", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-修改网店商家编码")
    public EshopProductSkuPageData modifyPlatformXcode(@RequestBody EshopProductSkuPageData pageData) {
        manageService.modifyPlatformXcode(pageData);
        return pageData;
    }

    @RequestMapping(value = "/modifyLocalXcode", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-修改系统商家编码")
    public EshopProductSkuPageData modifyLocalXcode(@RequestBody EshopProductSkuPageData pageData) {
        manageService.modifyLocalXcode(pageData);
        return pageData;
    }

    @RequestMapping(value = "/attrRelationList", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-属性对应关系列表")
    public List<EshopProductSkuAttrRelation> attrRelationList(@RequestBody List<EshopProductSkuPageData> pageDataList) {
        if (CollectionUtils.isEmpty(pageDataList)) {
            return new ArrayList<>();
        }
        return manageService.getAttrRelationList(pageDataList);
    }

    @RequestMapping(value = "/queryAttrRelationsByShop", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-属性对应关系列表")
    public List<EshopProductSkuAttrRelation> queryAttrRelationsByShop(@RequestBody BigInteger otypeId) {
        if (otypeId == null || BigInteger.ZERO.equals(otypeId)) {
            return new ArrayList<>();
        }
        return manageService.queryAttrRelationsByShop(otypeId);
    }

    @RequestMapping(value = "/saveEshopAttrRelation", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-保存属性对应关系")
    public boolean saveEshopAttrRelation(@RequestBody List<EshopProductSkuAttrRelation> attrRelationList) {
        manageService.saveEshopAttrRelation(attrRelationList);
        return true;
    }

    @ApiOperation("网店商品管理-本地属性列表")
    @RequestMapping(value = "/queryLocalProps", method = RequestMethod.POST)
    public List<Prop> queryLocalProps() {
        return manageService.queryLocalProps();
    }

    @RequestMapping(value = "/doBindRelation", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-手工绑定对应关系")
    public String doBindRelation(@RequestBody List<EshopProductSkuPageData> pageDataList) {
        String result = "";
        try {
            manageService.doBindRelation(pageDataList);
        } catch (Exception ex) {
            result = ex.getMessage();
        }
        return result;
    }


    @RequestMapping(value = "/batchModifyStockSyncState", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-批量修改库存同步状态")
    public String batchModifyStockSyncState(@RequestBody EshopBatchModifyStockSyncRequest request) {
        return manageService.batchModifyStockSyncState(request);
    }

    @RequestMapping(value = "/queryMultiDetails", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-查询时效库存明细")
    public List<MultiStockSyncDetail> queryMultiDetails(@RequestBody EshopQueryMultiDetailsRequest request) {
        return manageService.queryMultiDetails(request);
    }

    @RequestMapping(value = "/saveDefaultRule", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-保存默认规则")
    public String saveDefaultRule(@RequestBody StockSyncRule rule) {
        try {
            return manageService.saveDefaultRule(rule, true);
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    /**
     * 需要用到的参数:id,otypeid,autoSyncEnabled,eshopType
     */
    @RequestMapping(value = "/updateAutoSyncStockEnabled", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-自动同步库存开关")
    public String updateAutoSyncStock(@RequestBody StockSyncRule rule) {
        try {
            return manageService.updateAutoSyncStockEnabled(rule);
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @RequestMapping(value = "/queryStock", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-查询库存")
    public List<EshopProductSkuPageData> queryStockByPageData(@RequestBody List<EshopProductSkuPageData> pageDataList) {
        return stockSvc.queryStockByPageData(pageDataList, false);
    }

    @RequestMapping(value = "/queryMark", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-查询商品标记")
    public List<EshopProductSkuPageData> queryMarkByPageData(@RequestBody List<EshopProductSkuPageData> pageDataList) {
        if (CollectionUtils.isEmpty(pageDataList)) {
            return pageDataList;
        }
        manageService.buildMarkByPageData(pageDataList,false);
        return pageDataList;
    }

    @RequestMapping(value = "/createPtype", method = RequestMethod.POST)
    @ApiOperation("平台商品管理-创建系统商品")
    public String createPtype(@RequestBody CreatePtypeParams params) {
        String taskId = "createPtype" + UId.newId();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        manageService.createLocalPtype(params, processLogger);
        return taskId;
    }


    @RequestMapping(value = "/quickCreateCombo", method = RequestMethod.POST)
    @ApiOperation("平台商品管理-快速创建系统套餐")
    public ComboSaveResponse createCombo(@RequestBody CreateComboParams params) {
        return manageService.createCombo(params);
    }

    @PageDataSource
    @PostMapping("/queryCustomRule")
    @ApiOperation("网店商品管理-自定义库存同步规则列表查询")
    public PageResponse<StockSyncRule> queryCustomRule(@RequestBody PageRequest<QueryStockRuleParameter> query) {
        QueryStockRuleParameter parameter = query.getQueryParams();
        CommonUtil.initLimited(parameter);
        PageDevice.initPage(query);
        return PageDevice.readPage(manageService.queryCustomRules(parameter));
    }

    @RequestMapping(value = "/saveCustomRule", method = RequestMethod.POST)
    @ApiOperation("平台商品管理-保存默认规则")
    public String saveCustomRule(@RequestBody StockSyncRule rule) {
        try {
            return manageService.saveCustomRule(rule);
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @RequestMapping(value = "/bindRuleId", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-批量修改库存同步状态")
    public String bindRuleId(@RequestBody EshopProductBindRuleIdRequest request) {
        return manageService.bindRuleId(request);
    }

    @RequestMapping(value = "/batchDeleteCustomRule", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-批量删除自定义库存规则")
    public String batchDeleteCustomRule(@RequestBody EshopBatchDeleteCustomRuleRequest request) {
        try {
            return manageService.batchDeleteCustomRule(request);
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @RequestMapping(value = "/saveProductMark", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-打标")
    public String updateMark(@RequestBody EshopUpdateProductMarkRequest request) {
        if (request.getAsync() != null && request.getAsync()) {
            String taskId = UId.newId().toString();
            ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
            request.setProcessLogger(processLogger);
            ThreadPool threadPool = ThreadPoolFactory.build("load-upload");
            threadPool.executeAsync(invoker -> {
                try {
                    manageService.saveProductMark(request);
                } catch (Exception ex) {
                    processLogger.appendMsg(ex.getMessage());
                } finally {
                    processLogger.doFinish();
                }
            }, "网店商品打标");
            return taskId;
        } else {
            return manageService.saveProductMark(request);
        }
    }

    @PostMapping("/getStockLog")
    @ApiOperation("查询库存同步日志")
    public PageResponse<EshopStockSyncLogger> queryStockSyncLog(@RequestBody PageRequest<QueryStockSyncLogParameter> pageRequest) {
        QueryStockSyncLogParameter queryParams = pageRequest.getQueryParams();
        queryParams.setProfileId(CurrentUser.getProfileId());
        return LogService.query(pageRequest);
    }

    @RequestMapping(value = "/queryMultiStockSync", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-查询线上商品分仓库存和时效库存信息")
    public List<EshopProductSkuPageData> queryMultiStockSync(@RequestBody List<EshopProductSkuPageData> pageDataList) {
        if (CollectionUtils.isEmpty(pageDataList)) {
            return pageDataList;
        }
        return stockSvc.queryStockByPageData(pageDataList, true);
    }

    /**
     * 批量解除手工绑定关系
     */
    @RequestMapping(value = "/batchUnbindManualRelation", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-批量解除手工绑定")
    public String batchUnbindManualRelation(@RequestBody List<EshopProductSkuPageData> pageDataList) {
        String result = "";
        try {
            manageService.batchUnbindManualRelation(pageDataList);
        } catch (Exception ex) {
            result = ex.getMessage();
        }
        return result;
    }

    @RequestMapping(value = "/syncStock", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-同步库存")
    public String syncStock(@RequestBody List<EshopProductSkuPageData> skuPageDataList) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        ThreadPool threadPool = ThreadPoolFactory.build(STOCK_SYNC_THREAD_NAME);
        threadPool.executeAsync(invoker -> {
            try {
                manageService.syncStock(skuPageDataList, processLogger);
            } catch (Exception ex) {
                processLogger.appendMsg(ex.getMessage());
            } finally {
                processLogger.doFinish();
            }
        }, "同步库存");
        return taskId;
    }

    @RequestMapping(value = "/deleteSkus", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-删除SKU")
    public void deleteSkus(@RequestBody List<EshopProductSkuPageData> skus) {
        manageService.deleteSkus(skus);
    }

    @RequestMapping(value = "/syncProductRelationToSonShop", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-同步商品数据到子店")
    public List<EshopInfo> syncProductRelationToSonShop(@RequestBody List<EshopInfo> eshopInfos) {
        manageService.syncProductRelationToSonShop(eshopInfos);
        return eshopInfos;
    }

    @RequestMapping(value = "/supportStockSyncShopTypes", method = RequestMethod.POST)
    @ApiOperation("网店商品管理-获取支持库存同步的网店类型")
    public SupportStockSyncShopTypeResponse supportStockSyncShopTypes() {
        SupportStockSyncShopTypeResponse response= new SupportStockSyncShopTypeResponse();
        response.setGeneralStockSyncShopTypes(EshopUtils.listFeatureSupportedShopTypes(FeatureEnum.STOCK_SYNC.getFeature()));
        response.setWarehouseStockSyncShopTypes(EshopUtils.listFeatureSupportedShopTypes(FeatureEnum.WAREHOUSE_SYNC.getFeature()));
        return response;
    }

    @RequestMapping(value = "/queryEshopSaleStockBySkuids", method = RequestMethod.POST)
    @ApiOperation("通过商品skuid查询网店可销售库存")
    public List<QueryEshopSqlSkuData> queryEshopSaleStockBySkuids(@RequestBody QueryEshopSqlSkuParam queryParam) {
        return stockSvc.queryEshopSaleStockBySkuids(queryParam);
    }
}
