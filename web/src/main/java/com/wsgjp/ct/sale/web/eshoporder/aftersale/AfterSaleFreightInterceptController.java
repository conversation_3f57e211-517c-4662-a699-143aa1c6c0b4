package com.wsgjp.ct.sale.web.eshoporder.aftersale;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.wsgjp.ct.pm.annotation.PermissionCheck;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.refund.AfterSaleFreightInterceptStatus;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopRelationComboExcelEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopRelationProductExcelEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.freightintercept.AfterSaleFreightInterceptEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.freightintercept.AfterSaleInterceptExcelEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.freightintercept.InterceptStatusConverter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.aftersale.AssignInterceptStatusRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.aftersale.QueryFreightInterceptParameter;
import com.wsgjp.ct.sale.biz.eshoporder.service.aftersale.manager.*;
import com.wsgjp.ct.sale.common.constant.PermissionSysConst;
import io.swagger.annotations.Api;
import jodd.io.FileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "售后管理")
@RequestMapping("${app.id}/eshoporder/aftersale")
@RestController
public class AfterSaleFreightInterceptController {
    private final AfterSaleFreightInterceptService interceptService;


    private static final Logger logger = LoggerFactory.getLogger(AfterSaleFreightInterceptController.class);

    public AfterSaleFreightInterceptController(AfterSaleFreightInterceptService interceptService) {

        this.interceptService = interceptService;
    }
    /**
     * 查询售后单列表
     */
    @PostMapping(value = "/queryAftersaleFreightInterceptCount")
    @PermissionCheck(key = PermissionSysConst.ESHOP_REFUND_VIEW)
    public PageResponse<AfterSaleFreightInterceptEntity> queryAftersaleFreightInterceptCount(@RequestBody PageRequest<QueryFreightInterceptParameter> parameter) {
        return interceptService.queryAfterSaleFreightInterceptPageInfo(parameter);
    }

    /**
     * 查询售后单列表
     */
    @PostMapping(value = "/queryAftersaleFreightInterceptList")
    @PermissionCheck(key = PermissionSysConst.ESHOP_REFUND_VIEW)
    public PageResponse<AfterSaleFreightInterceptEntity> queryAftersaleFreightInterceptList(@RequestBody PageRequest<QueryFreightInterceptParameter> parameter) {
        return interceptService.queryAfterSaleFreightInterceptPageInfo(parameter);
    }

    /**
     * 处理物流拦截状态
     */
    @PostMapping(value = "/assignFreightInterceptStatus")
    @PermissionCheck(key = PermissionSysConst.ESHOP_REFUND_VIEW)
    public String assignFreightInterceptStatus(@RequestBody AssignInterceptStatusRequest assignInterceptStatusRequest) {
        return interceptService.assignFreightInterceptStatus(assignInterceptStatusRequest);
    }
    /**
     * 处理线上物流拦截状态
     */
    @PostMapping(value = "/doOnlineFreightIntercept")
    @PermissionCheck(key = PermissionSysConst.ESHOP_REFUND_VIEW)
    public String doOnlineFreightIntercept(@RequestBody AssignInterceptStatusRequest assignInterceptStatusRequest) {
        return interceptService.doOnlineFreightIntercept(assignInterceptStatusRequest);
    }

    @PostMapping("/doInterceptExport")
    @ResponseBody
    public String downloadRelationModalWithQuery(HttpServletResponse response, @RequestBody PageRequest<QueryFreightInterceptParameter> parameter) {
        List<AfterSaleFreightInterceptEntity> entities = interceptService.queryAfterSaleFreightIntercept(parameter.getQueryParams());
        try {
            String excelFileName = "物流拦截导出.xls";
            // 生成数据
            List<AfterSaleInterceptExcelEntity> productList = new ArrayList();
            for (AfterSaleFreightInterceptEntity en :entities) {
                AfterSaleInterceptExcelEntity excelEntity = new AfterSaleInterceptExcelEntity();
                excelEntity.setFreightBillNo(en.getFreightNo());
                excelEntity.setFreightName(en.getFreightName());
                excelEntity.setInterceptStatus(en.getInterceptStatus());
                productList.add(excelEntity);
            }
            InputStream templateFileName =FileUtil.class.getClassLoader().getResourceAsStream("static/sale/eshoporder/template/物流拦截导出模板.xls");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName= URLEncoder.encode(excelFileName, "UTF-8");
            response.addHeader("Content-disposition", "attachment;filename=" + fileName);
//            ExcelWriter excelWriter = EasyExcel.write(new BufferedOutputStream(response.getOutputStream()))
//                    .withTemplate(templateFileName)
//                    .registerConverter(new InterceptStatusConverter()).build();
//            WriteSheet test1 = EasyExcel.writerSheet(1, "物流拦截信息").build();
//            excelWriter.write(productList, test1);
//            excelWriter.finish();
            EasyExcel.write(new BufferedOutputStream(response.getOutputStream())).registerConverter(new InterceptStatusConverter()).withTemplate(templateFileName).sheet().doFill(productList);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

}
