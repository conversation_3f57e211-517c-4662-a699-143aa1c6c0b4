Type.registerNamespace('sale.eshoporder.aftersale');

sale.eshoporder.aftersale.AftersaleFreightInterceptImportAction = function () {
    sale.eshoporder.aftersale.AftersaleFreightInterceptImportAction.initializeBase(this);
};

sale.eshoporder.aftersale.AftersaleFreightInterceptImportAction.prototype = {
    context: function (cb) {

        cb({});
    },
    uploadFile:function (sender) {
        var form = this.get_form();
        $analysisCloud.Util.addPubSystemLog("进入物流拦截导入");
        form.grid.dataBind("");
        var files = this.get_form().file.get_element().files;
        var length = files.length;
        $common.checkError(length < 1, "请选择文件");
        var suffix = files[0].name.substring(files[0].name.lastIndexOf(".")+1).toLowerCase();
        $common.checkError(suffix != "xlsx" && suffix != "xls","此文件不支持上传，请重新选择！")
        $common.checkError(files[0].size > (parseInt(4) * 1024 * 1024), "文件大小超过" + 4 + "M,请减少数据再提交");
        form.submitBtn.set_enabled(false);
        var panelID = sender.get_tag(); // 这样做的目的是平台内部会根据panel.saveData()局部读取数据，而不是form.saveData()会触发全局验证

        Sys.UI.Controls.FileUpload.submit(this.get_form()[panelID], // 只会当前控件对应容器的FileUpload触发panel的saveData
            $createDelegate(this, this.doImportSucceeded),
            $createDelegate(this, this.doImportFailed),
            function (data) { // 支持动态构造服务端需要的参数，因此不是必须再gspx写隐藏域<HiddenField>也可以传参
                data.eshopId = form.get_pageParams().queryParams.otypeId;
            }, {});
    },

    doImportSucceeded: function (sender, result) {
        var _this = this;
        if (result.code != '200') {
            Sys.UI.MessageBox.alert("导入失败：" + result.message);
            return;
        }
        _this._activeTimer = new Sys.Timer();
        this._stop = false;
        var intervalCount = 1000;
        _this._activeTimer.set_interval(intervalCount);
        _this._activeTimer.set_enabled(true);
        _this._activeTimer.add_tick(Function.createDelegate(this, function () {
            _this.getMessage(result.data);
        }));
    },

    doImportFailed: function (sender, result) {
        var form = this.get_form();
        if (result) {
            $common.alert(result);
        }
    },
    getMessage: function (processId) {
        var form = this.get_form();
        if (null == form) {
            return;
        }
        var requestParam=new Object();
        requestParam.requestStr=processId;
        var _this = this;

        form.processBar.set_value(80);
        this.get_service().post("sale/eshoporder/common/getProcessMsg",requestParam,
            function(res){
                var obj=res.data;
                if (!obj) {
                    return;
                }
                if (obj.completed) { //已经结束
                    form.processBar.set_value(100);
                    _this._activeTimer.set_enabled(false);
                    form.exportError.set_enabled(true);
                    form.submitBtn.set_enabled(true);
                    // form.btnStart.set_enabled(true);
                    if (obj.message) {
                        var appendlist = obj.message.split("!");
                        for(var i=0;i<appendlist.length;i++){
                            _this.get_form().grid.appendRowData({message: appendlist[i],logtype:"信息"});
                        }
                        _this._stop = true;
                        _this._returnobj = obj.returnobj;
                    }
                }
            }, function (error) {
                if (_this._activeTimer) {
                    _this._activeTimer.set_enabled(false);
                }
                _this._stop = true;
                alert(error);
            }
            , false);
    },

    exportExcel: function (sender) {
        var form = sender.get_form();
        form.export.exportData();
    },

    initialize: function AftersaleFreightInterceptImportAction$initialize() {
        sale.eshoporder.aftersale.AftersaleFreightInterceptImportAction.callBaseMethod(this, 'initialize');
    },

    dispose: function () {
        sale.eshoporder.aftersale.AftersaleFreightInterceptImportAction.callBaseMethod(this, 'dispose');
    }
};
sale.eshoporder.aftersale.AftersaleFreightInterceptImportAction.registerClass('sale.eshoporder.aftersale.AftersaleFreightInterceptImportAction', Sys.UI.PageAction);
