<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="导入物流拦截"
      ActionType="sale.eshoporder.aftersale.AftersaleFreightInterceptImportAction, sale/eshoporder/aftersale/AftersaleFreightInterceptImport.js" CssClass='pd0 shglPage jarvis'>
    <VBlock>
        <Block Tag="拦截状态" ID="interceptStatusBlock" CssClass="EditBlock ItemEdit">
            <Label Text="拦截状态:"/>
            <DropDownCheckBoxList ID="dpInterceptStatus"
                                  ReportField="拦截状态"
                                  DropDownStyle="DropDownSearch"
                                  LayoutDirection="Vert"
                                  ListItems="0=待拦截,1=已发起拦截,2=拦截成功,3=拦截失败"
                                  DataTextField="name"
                                  SelectedIndex="1"
                                  DataField="interceptStatus"
                                  DataValueField="code" CssClass='FlexAuto'
            />
        </Block>
        <FileUpload WebMethod="sale/eshoporder/aftersale/importInterception">
            <HPanel ID="fileUpload">
                <FileEdit Label="选择文件：" ID="file" Accept=".xls,.xlsx" DataField="file" Width="200"    />
                <Button ID="submitBtn" Text="开始导入" OnClick="uploadFile" Tag="fileUpload"/>
            </HPanel>
        </FileUpload>

        <HBlock CssStyle="display:flex">
            <ProgressBar ID="processBar" Value="0"/>
            <!-- <SpeedButton Icon='aicon-check-circle-fill' ID="okFlag" Enabled="false" CssStyle="color:#6fc544;line-height: 12px;height: 15px;" CssClass='icononly'/>
             <SpeedButton Icon='aicon-close-circle-fill' ID="errFlag" Visible="false" CssStyle="color:#e85854;line-height: 12px;height: 15px;" CssClass='icononly'/>
             -->
        </HBlock>
        <Grid ID="grid" Height="200" WordWrap="true" AllowConfig="false" MaxHeight="300" Pager="Bottom" ShowLines="false">
            <DropDownColumn TextAlign="Center" Visible="false" DataField="logType" Caption="类型" ListItems="ERROR=错误,WARN=警告,SUCCESS=成功,INFO=信息,DEBUG=调试" ReadOnly="true"/>
            <Column Caption="日志输出" ReadOnly="true" DataField="message" AllowStretch="true"/>
        </Grid>
        <HBlock HAlign="Center">
            <CancelButton Text="关闭"/>
        </HBlock>
    </VBlock>
</Page>