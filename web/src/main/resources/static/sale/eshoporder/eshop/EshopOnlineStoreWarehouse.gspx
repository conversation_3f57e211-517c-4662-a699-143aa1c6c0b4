<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="网店仓库对应" Icon="bicon-quanqudaomendianduiying"
      ActionType="sale.eshoporder.eshop.EshopOnlineStoreWarehouseAction,sale/eshoporder/eshop/EshopOnlineStoreWarehouse.js">
    <FlexColumn>
        <FlowPanel CssClass="BorderBottom">
            <!--<DropDownCheckBoxList ID="baseOtype" Label="网店:" DataSource="${otypeList}"
                          DataTextField="fullname" NullDisplayText="请选择网店"
                          DataValueField="id" DropDownStyle="DropDownSearch" OnChange="doChangeBaseOtype"/>-->
            <DropDownCheckBoxList ID="baseOtype" Label="网店" ReportVisible="true"
                                  SelectOnly="true" Required="true"
                                  NullDisplayText="全部"
                                  DropDownStyle="DropDownMultiSearch" CssClass='FlexAuto'
                                  OnChange="doChangeBaseOtype"
                                  DataSource="${otypeList}"
                                  DataField="id"
                                  DataTextField="fullname"
                                  DataValueField="id"
                                  Width="280"/>
            <TextEdit ID="platformStoreStockId" Label="网店仓库" NullDisplayText="ID/名称" MaxLength="200"/>
            <DropDownEdit ID="platformStoreType" Label='网店仓库类型:' SelectedIndex="0" DropDownStyle="DropDownList"
                          DropDownRows="0"
                          ListItems="0=全部,1=门店,2=商家仓,3=平台仓"/>
            <DropDownEdit ID="correspondFlag" Label='对应状态:' SelectedIndex="0" DropDownStyle="DropDownList"
                          DropDownRows="0"
                          ListItems="2=全部,0=未对应,1=已对应"/>
            <Button Text="查询" CssClass="BorderButton" OnClick="selectList"/>
        </FlowPanel>

        <FlowPanel>
            <Button Text="刷新" CssClass='SpecialButton' OnClick="doRefresh"  Enabled="eshoporder.onlineStoreWarehouse.set"/>
            <Button Text="添加网店仓库" OnClick="addPlatformStore" PopupMenu="operation"  Enabled="eshoporder.onlineStoreWarehouse.set"/>
            <Button Text="删除" OnClick="deleted" Enabled="eshoporder.onlineStoreWarehouse.set"/>
            <Block CssClass='Flex1'/>
            <Button Text="筛选" Flat="true" CssClass="aicon-filter" OnClick="doShowFilter"/>
        </FlowPanel>
        <!--        DataSource='${list}'-->
        <Grid ID="grid" CssClass='QCDGrid' Pager='Bottom' OnFilter="doGetOtherFilter"
              OnCellBeginEdit="doCellBeginEdit" ReadOnly="false"
              BindPagerDataSource="gridPagerDataSource" OnEmpty='doBindEmpty'>
            <MultiSelectColumn ShowHeaderCheckBox="true" ReadOnly="false" Width="30" DataField="selected"
                               AllowConfig="false" AllowFilter="false" AllowFrozen="false"/>
            <DropDownColumn Caption="网店" DataSource="${filterOtypeList}" DataField="eshopId"
                            DataTextField="fullname" AllowStretch='true' ReadOnly="true"
                            DataValueField="id" DropDownStyle="DropDownSearch"/>
            <TextColumn Caption="网店仓库ID"  DataField="platformStoreStockId"
                        AllowStretch='true' OnChange="updatePlatformStoreInfo" ReadOnly="${changeFlag}"
                        Icon='aicon-bianji4'/>
            <TextColumn Caption="网店仓库名称"  DataField="platformStoreName"
                        AllowStretch='true' OnChange="updatePlatformStoreInfo"  ReadOnly="${changeFlag}"
                        Icon='aicon-bianji4'/>
            <TextColumn Caption="网店仓库地址"
                        DataField="platformStoreAddress" AllowStretch='true' OnChange="updatePlatformStoreInfo"  ReadOnly="${changeFlag}"
                        Icon='aicon-bianji4'/>
            <DropDownColumn ID="platformStoreType" HeaderCssClass='color2' Caption="网店仓库类型" AllowStretch='true'
                            OnChange="doPlatformStoreTypeChange"
                            ReadOnly="${changeFlag}"
                            ListItems="1=门店,2=商家仓,3=平台仓" DataField="platformStoreType"
                            DataTextField="fullname"
                            DataValueField="id" DropDownStyle="DropDownSearch" Icon='aicon-bianji4'/>
<!--            <TextColumn DataField="platformStoreTypeTags" Caption="网店仓库类型"-->
<!--                        ReadOnly="true" AllowSort="false" AllowStretch='true' AllowHTML="true" OnFilterRendering='doFilterRendering'-->
<!--                        ReportVisible="false"/>-->
            <SwitchButtonColumn Caption="对应关系" ReadOnly="true" Text='未对应,已对应' DataField="correspondFlag"
                                CheckedValue='true'
                                AllowStretch='true'/>
            <DropDownColumn ID="ktypeId" HeaderCssClass='color2' Caption="系统仓库名称" AllowStretch='true'
                            OnChange="doKtypeChange"
                            ReadOnly="${changeFlag}"
                            DataSource="${ktypeList}" DataField="ktypeId"
                            DataTextField="fullname"
                            DataValueField="id" DropDownStyle="DropDownSearch"/>
            <TextColumn Caption="系统仓库地址" HeaderCssClass='color2' ReadOnly="true" DataField="btypeAddress"
                        AllowStretch='true' AllowFilter="false"/>
            <!--<DropDownColumn ID="source" HeaderCssClass='color2' Caption="来源" AllowStretch='true'
                            ReadOnly="${changeFlag}"
                            ListItems="0=线上,1=手工" DataField="source"
                            DataTextField="source"
                            DataValueField="id" DropDownStyle="DropDownSearch" Visible = 'false'/>-->
            <!--<IconColumn Caption="操作" ListItems="删除=aicon-shanchu" Visible="${!changeFlag}" AllowConfig="false"
                        AllowSort="false" OnClick="deleted"/>-->
        </Grid>

    </FlexColumn>

    <CustomControl ID='import'>
        <VPanel>
            <FileUpload Label="数据文件:" WebMethod="sale/eshoporder/eshopplatformstoremapping/saveFileNew">
                <HPanel ID="fileUpload">
                    <FileEdit Width="300" DataField="importFile"  Hint="选择需要导入的网店仓库数据文件（Excel）"
                              Accept='/*'/>
                </HPanel>
            </FileUpload>
            <SpeedButton CssClass='BorderButton' Text="下载(网店仓库导入模板)" Icon='aicon-xiazai2' OnClick="doDownloadTemplate"/>
            <!--
            <DropDownEdit Label="网店仓库:" Width="300" NullDisplayText="仅支持单选" ID="ktypeId"
                          DataSource="${ktypeList}"
                          DataTextField="fullname"
                          DataValueField="id" DropDownStyle="DropDownSearch"/>
            -->
            <VPanel ID="panelInfo" Caption="操作进度" HAlign="Center">
                <MemoEdit ID="messageInfo" Height="260" Width="610" Rows="10000" ReadOnly="true" TabStop="true"
                          CssStyle="border:none;text-indent:0;padding-left:5px"/>
            </VPanel>
            <VSpacer/>
            <HPanel HAlign="Right">
                <Button ID="btnStart" Text="开始" OnClick="importFile" Tag="fileUpload"/>
                <Button ID="btnStop" Text="停止" OnClick="doStopProgress"/>
                <CancelButton Text="退出"/>
            </HPanel>
        </VPanel>
    </CustomControl>

    <CustomControl CssClass='pd0' ID='importError'>
        <FlexBlock CssClass='plr20 ptb10'>
            <Grid ID="importErrorGrid" AutoMaxRowCount="5" DefaultRowCount="1"
                  ReadOnly='true' DefaultShowEditor='false' DataSource="${errorList}">
                <TextColumn Caption="错误行" ReadOnly="true" DataField="rowIndex" Width="60"/>
                <TextColumn Caption="错误原因" ReadOnly="true" DataField="errorMsg" AllowStretch='true'/>
            </Grid>
        </FlexBlock>
        <HBlock CssClass='BottomBlock'>
            <Block CssClass='Flex1'/>
            <OkButton CssClass='SpecialButton'/>
            <CancelButton/>
            <Button Text="导出错误信息" CssClass='SpecialButton'>
                <ReportAction ID='export' ReportMode='CurrentPage' Method='exportData'
                              Grid='importErrorGrid' ReportName='网店仓库导入出错信息表格'/>
            </Button>
        </HBlock>
    </CustomControl>

    <CustomControl ID='addPlatformArtesianRotation'>
        <VPanel>
            <DropDownEdit ID="newBaseOtype" Required="true" Label="网店:" DataSource="${filterOtypeList}" Width="220"
                          DataTextField="fullname" DataField="eshopId"
                          DataValueField="id" DropDownStyle="DropDownSearch" LabelStyle="float:left" LabelCssClass="MustCharLeft"/>
            <VSpacer Height="5"/>
            <DropDownEdit ID="newLocalKtype" Required="true" Label="本地仓库:" Width="220"
                          DataSource="${ktypeList}"
                          DataTextField="fullname"
                          DataValueField="id" DropDownStyle="DropDownSearch" LabelStyle="float:left" LabelCssClass="MustCharLeft" DataField="ktypeId"/>
            <VSpacer Height="5"/>
            <TextEdit ID="newPlatformCode" DataField="platformStoreStockId" Required="true" Label="网店仓库ID:" Width="220" MaxLength="100" LabelStyle="float:left" LabelCssClass="MustCharLeft"/>
            <VSpacer Height="5"/>
            <TextEdit ID="platformStoreName" DataField="platformStoreName" Required="true" Label="网店仓库名称:" Width="220" MaxLength="100" LabelStyle="float:left" LabelCssClass="MustCharLeft"/>
            <VSpacer Height="5"/>
            <TextEdit ID="platformStoreAddress" DataField="platformStoreAddress" Label="网店仓库地址:" LabelStyle="float:left" Width="220" MaxLength="100"/>
            <VSpacer Height="5"/>
            <DropDownEdit ID="addPlatformStoreType" Required="true"  Label="网店仓库类型:" LabelStyle="float:left" LabelCssClass="MustCharLeft" DropDownStyle="DropDownList"
                          DropDownRows="0"
                          DataField="platformStoreType"
                          SelectedIndex="0"
                          Width="220"
                          ListItems="1=门店,2=商家仓,3=平台仓"/>
            <VSpacer Height="20"/>
            <HPanel HAlign="Right">
                <Button Text="确认" CssClass="SpecialButton" OnClick="doSave"/>
                <CancelButton/>
            </HPanel>
        </VPanel>
    </CustomControl>
    <PopupMenu ID="operation">
        <MenuItem Text="导入网店仓库"   ID="ImportOnlineStoreWarehouse" OnClick="importPlatformStore"/>
    </PopupMenu>
    <Style>
        .QCDGrid .ybtn{border:1px solid #EC9C2D;color:#EC9C2D;padding:1px 4px;margin:0 2px;border-radius:8px;}
        .QCDGrid .color1{background-color:#FFF5EA}
        .QCDGrid .color2{background-color:#EAF1FE}
        .QCDGrid font{border-radius:4px;line-height:22px;padding:3px 4px;}
        .QCDGrid font.status1{background-color:#22C977;color:#fff;margin:0 3px;}
        .QCDGrid font.status2{background-color:#fdb823;color:#fff;margin:0 3px;}
    </Style>
</Page>