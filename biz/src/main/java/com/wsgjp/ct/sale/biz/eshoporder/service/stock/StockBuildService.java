package com.wsgjp.ct.sale.biz.eshoporder.service.stock;

import com.wsgjp.ct.sale.biz.eshoporder.config.SaleBizConfig;
import com.wsgjp.ct.sale.biz.eshoporder.constant.StringConstant;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.ComboDetail;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.Pcategory;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleSyncType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuPageData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryComboParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryProductMarkRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QuerySkuMappingRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.StockCalByComboParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.StockCalParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.*;
import com.wsgjp.ct.sale.biz.eshoporder.impl.stock.StockSyncCalculateFixedImpl;
import com.wsgjp.ct.sale.biz.eshoporder.impl.stock.StockSyncCalculateFrozenImpl;
import com.wsgjp.ct.sale.biz.eshoporder.impl.stock.StockSyncCalculateShareImpl;
import com.wsgjp.ct.sale.biz.eshoporder.impl.stock.StockSyncCalculateVirtualImpl;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.EshopProductDataService;
import com.wsgjp.ct.sale.biz.eshoporder.support.StockSyncCalculate;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.platform.dto.product.MultiTimeStock;
import com.wsgjp.ct.sale.platform.dto.product.WareHouseStockSync;
import com.wsgjp.ct.sale.sdk.common.SaleUtil;
import com.wsgjp.ct.sale.sdk.stock.biz.QueryStockService;
import com.wsgjp.ct.sale.sdk.stock.entity.*;
import com.wsgjp.ct.sale.sdk.stock.parameter.QueryLockRecordParameter;
import com.wsgjp.ct.sale.sdk.stock.parameter.QueryStockParameter;
import com.wsgjp.ct.support.business.Money;
import com.wsgjp.ct.support.business.MoneyUtils;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 28/4/2020 上午 10:25
 */
@Service
public class StockBuildService {
    private final QueryStockService qtyService;
    private final EshopOrderBaseInfoService baseSvc;
    private final StockRuleService ruleSvc;
    private final StockOversoldConfigService setSafeQtyConfigSvc;

    private final EshopProductDataService productDataService;
    private final SaleBizConfig bizConfig;
    private static final Logger logger = LoggerFactory.getLogger(StockBuildService.class);

    static Map<StockRuleType, StockSyncCalculate> calculateMap = new HashMap<>();

    static {
        calculateMap.put(StockRuleType.FrozenStock, new StockSyncCalculateFrozenImpl());
        calculateMap.put(StockRuleType.FixedStockNumber, new StockSyncCalculateFixedImpl());
        calculateMap.put(StockRuleType.SharedInventory, new StockSyncCalculateShareImpl());
        calculateMap.put(StockRuleType.VirtualStock, new StockSyncCalculateVirtualImpl());
    }

    public StockBuildService(QueryStockService qtyService, EshopOrderBaseInfoService baseSvc, StockRuleService ruleSvc, StockOversoldConfigService setSafeQtyConfigSvc, EshopProductDataService productDataService, SaleBizConfig bizConfig) {
        this.qtyService = qtyService;
        this.baseSvc = baseSvc;
        this.ruleSvc = ruleSvc;
        this.setSafeQtyConfigSvc = setSafeQtyConfigSvc;
        this.productDataService = productDataService;
        this.bizConfig = bizConfig;

    }

    public BigDecimal getSyncQty(BigInteger skuId, StockCalParameter parameter) {
        StockSyncRule rule = parameter.getRule();
        StockRuleType ruleType = rule.getRuleType();
        StockSyncCalculate calculate = calculateMap.get(ruleType);
        return calculate.doCalculate(skuId, parameter);
    }

    public BigDecimal getSyncQtyByCombo(StockCalByComboParameter parameter) {
        StockSyncRule rule = parameter.getRule();
        StockRuleType ruleType = rule.getRuleType();
        StockSyncCalculate calculate = calculateMap.get(ruleType);
        return calculate.doCalculateCombo(parameter);
    }

    /**
     * 根据platformskuid和网店查询库存
     *
     * @param eshopId
     * @param platformNumId
     * @param platformSkuId
     * @param ktypeId
     * @return
     */
    public List<StockSendQtyEntity> getSendQtyListByPlatformInfo(BigInteger eshopId, String platformNumId, String platformSkuId, BigInteger ktypeId) {
        try {
            //查询sku
            EshopProductSkuMapping productSku = productDataService.getProductSku(eshopId, platformNumId, platformSkuId);
            if (productSku == null) {
                return new ArrayList<>();
            }
            QuerySkuMappingRequest request = new QuerySkuMappingRequest();
            request.setProfileId(CurrentUser.getProfileId());
            request.setOtypeId(eshopId);
            request.setOpenXcode(MappingType.XCODEMAPPING.equals(productSku.getMappingType()));
            request.setXcode(productSku.getXcode());
            request.setUniqueIdList(Collections.singletonList(productSku.getUniqueId()));
            EshopProductSkuMapping skuMapping = productDataService.getSingleSkuMappingForOrder(request);
            if (skuMapping == null || !skuMapping.isBind()) {
                return new ArrayList<>();
            }
            //查询到sku之后，根据sku的对应模式查询对应关系
            List<BigInteger> distinctSku = Collections.singletonList(skuMapping.getSkuId());
            List<BigInteger> distinctKtype = Collections.singletonList(ktypeId);
            QueryStockParameter parameter = new QueryStockParameter();
            parameter.setProfileId(CurrentUser.getProfileId());
            parameter.setKtypeIdList(distinctKtype);
            parameter.setSkuIdList(distinctSku);
            return qtyService.querySendQtyList(parameter);
        } catch (Exception e) {
            throw new RuntimeException(String.format("查询可发货库存报错【%s】", e.getMessage()), e);
        }
    }

    public List<StockSaleQtyEntity> getSaleQtyList(List<BigInteger> skuIds, List<BigInteger> ktypeIds) {
        try {
            if (doCheckParameterNull(skuIds, ktypeIds)) {
                return new ArrayList<>();
            }
            List<BigInteger> distinctSku = skuIds.stream().distinct().collect(Collectors.toList());
            List<BigInteger> distinctKtype = ktypeIds.stream().distinct().collect(Collectors.toList());
            QueryStockParameter parameter = new QueryStockParameter();
            parameter.setProfileId(CurrentUser.getProfileId());
            parameter.setKtypeIdList(distinctKtype);
            parameter.setSkuIdList(distinctSku);
            List<StockSaleQtyEntity> qtyList = qtyService.querySaleQtyList(parameter);
            return buildSaleQtyMergeKtype(qtyList);
        } catch (Exception e) {
            throw new RuntimeException(String.format("查询库存可销售库存报错【%s】", e.getMessage()), e);
        }
    }

    private boolean doCheckParameterNull(List<BigInteger> skuIds, List<BigInteger> ktypeIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return true;
        }
        return CollectionUtils.isEmpty(ktypeIds);
    }

    private List<StockSaleQtyEntity> buildSaleQtyMergeKtype(List<StockSaleQtyEntity> saleQtyEntities) {
        List<StockSaleQtyEntity> stockList = new ArrayList<>();
        if (CollectionUtils.isEmpty(saleQtyEntities)) {
            return stockList;
        }
        Map<BigInteger, StockSaleQtyEntity> mergeMap = new HashMap<>(3);
        for (StockSaleQtyEntity qty : saleQtyEntities) {
            BigInteger skuId = qty.getSkuId();
            if (!mergeMap.containsKey(skuId)) {
                mergeMap.put(skuId, qty);
                continue;
            }
            StockSaleQtyEntity exist = mergeMap.get(skuId);
            exist.setRecordQty(MoneyUtils.add(exist.getRecordQty(), qty.getRecordQty(), Money.Qty));
            exist.setStockQty(MoneyUtils.add(exist.getStockQty(), qty.getStockQty(), Money.Qty));
            exist.setLockQty(MoneyUtils.add(exist.getLockQty(), qty.getLockQty(), Money.Qty));
            exist.setLockRecordQty(MoneyUtils.add(exist.getLockRecordQty(), qty.getLockRecordQty(), Money.Qty));
            exist.setLockUsedQty(MoneyUtils.add(exist.getLockUsedQty(), qty.getLockUsedQty(), Money.Qty));
            exist.setSaleQty(MoneyUtils.add(exist.getSaleQty(), qty.getSaleQty(), Money.Qty));
        }
        return new ArrayList<>(mergeMap.values());
    }

    public List<StockSaleQtyEntity> getSaleQtyList(List<BigInteger> skuIds) {
        QueryStockParameter parameter = new QueryStockParameter();
        parameter.setProfileId(CurrentUser.getProfileId());
        parameter.setSkuIdList(skuIds);
        return qtyService.querySaleQtyList(parameter);
    }

    public List<StockSaleQtyBatchEntity> getBatchSaleQtyList(List<BigInteger> skuIds) {
        QueryStockParameter parameter = new QueryStockParameter();
        parameter.setProfileId(CurrentUser.getProfileId());
        parameter.setSkuIdList(skuIds);
        return qtyService.queryBatchSaleQtyList(parameter);
    }

    public List<StockLockQtyEntity> getLockRecordList(List<BigInteger> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return new ArrayList<>();
        }
        QueryLockRecordParameter parameter = new QueryLockRecordParameter();
        parameter.setProfileId(CurrentUser.getProfileId());
        parameter.setRuleIdList(ruleIds);
        return qtyService.queryLockRecordList(parameter);
    }

    public List<StockBatchLockQtyEntity> getBatchLockRecordList(List<BigInteger> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return new ArrayList<>();
        }
        QueryLockRecordParameter parameter = new QueryLockRecordParameter();
        parameter.setProfileId(CurrentUser.getProfileId());
        parameter.setRuleIdList(ruleIds);
        return qtyService.queryBatchLockRecordList(parameter);
    }

    public void buildSyncQtyByMapping(EshopInfo eshopInfo, List<EshopProductSkuMapping> mappingList) {
        if (CollectionUtils.isEmpty(mappingList)) {
            return;
        }
        List<StockSyncRule> syncRules = queryRuleListByMapping(eshopInfo, mappingList);
        List<BigInteger> skuIdList = getSkuIdListByMapping(mappingList);
        doBuildSyncQty(mappingList, skuIdList, syncRules);
    }

    private void doBuildSyncQty(List<EshopProductSkuMapping> mappingList, List<BigInteger> skuIdList, List<StockSyncRule> syncRules) {
        BigInteger profileId = CurrentUser.getProfileId();
        List<StockSaleQtyBatchEntity> saleQtyBatchList = getBatchSaleQtyList(skuIdList);
        List<StockSaleQtyEntity> saleQtyList = getSaleQtyList(skuIdList);
        List<BigInteger> ruleIds = mappingList.stream().map(EshopProductSkuMapping::getSyncRuleId).collect(Collectors.toList());
        List<StockBatchLockQtyEntity> lockBatchList = getBatchLockRecordList(ruleIds);
        List<StockLockQtyEntity> lockList = getLockRecordList(ruleIds);
        Map<BigInteger, List<SafeSaleQtyProductConfigEntity>> skuSafeQtyMap = buildSkuSafeQtyMap(profileId, skuIdList);
        Map<BigInteger, List<SafeSaleQtyConfigEntity>> safeQtyMap = buildSafeQtyMap();
        Map<BigInteger, List<StockSaleQtyBatchEntity>> batchSaleQtyMap = buildBatchSaleQtyMap(saleQtyBatchList);
        for (EshopProductSkuMapping mapping : mappingList) {

            //没有对应关系返回0
            if (!mapping.isBind() || (mapping.getPtypeId() == null || mapping.getPtypeId().equals(BigInteger.ZERO))) {
                mapping.setSyncQty(BigDecimal.ONE);
                continue;
            }
            BigInteger eshopId = mapping.getEshopId();
            Pcategory pcategory = mapping.getPcategory();
            StockSyncRule rule = queryRuleByMapping(mapping, syncRules);
            if (pcategory.equals(Pcategory.Combo)) {
                StockCalByComboParameter parameter = new StockCalByComboParameter();
                parameter.setRule(rule);
                parameter.setProfileId(profileId);
                parameter.setCombos(mapping.getComboDetailList());
                parameter.setEshopId(eshopId);
                parameter.setLockRecord(lockList);
                parameter.setSaleQtyList(saleQtyList);
                parameter.setSafeQtyConfigMap(safeQtyMap);
                parameter.setSkuSafeQtyMap(skuSafeQtyMap);
                BigDecimal qtyByCombo = getSyncQtyByCombo(parameter);
                mapping.setSyncQty(qtyByCombo);
                continue;
            }

            StockCalParameter parameter = new StockCalParameter();
            parameter.setRule(rule);
            parameter.setSaleQtyList(saleQtyList);
            if (batchSaleQtyMap.containsKey(mapping.getSkuId())) {
                parameter.setSaleQtyBatchList(batchSaleQtyMap.get(mapping.getSkuId()));
            }
            parameter.setLockRecord(lockList);
            parameter.setLockBatchRecord(lockBatchList);
            parameter.setEshopId(eshopId);
            parameter.setProfileId(profileId);
            parameter.setSafeQtyConfigMap(safeQtyMap);
            parameter.setSkuSafeQtyMap(skuSafeQtyMap);

            BigDecimal syncQty = getSyncQty(mapping.getSkuId(), parameter);
            mapping.setSyncQty(syncQty.divide(mapping.getUnitRate()).setScale(0, BigDecimal.ROUND_DOWN));
        }
    }

    public Map<BigInteger, List<SafeSaleQtyConfigEntity>> buildSafeQtyMap() {
        BigInteger profileId = CurrentUser.getProfileId();
        boolean useConfig = GlobalConfig.get(StringConstant.SAFE_QTY_SYS_DATA_KEY).equals(StringConstant.SYS_DATA_SUCCESS);
        if (!useConfig) {
            return null;
        }
        List<SafeSaleQtyConfigEntity> safeQtyList = setSafeQtyConfigSvc.querySafeSaleQtyConfigList(profileId);
        if (CollectionUtils.isEmpty(safeQtyList)) {
            return null;
        }
        return safeQtyList.stream().collect(Collectors.groupingBy(SafeSaleQtyConfigEntity::getKtypeId));
    }

    public Map<BigInteger, List<StockSaleQtyBatchEntity>> buildBatchSaleQtyMap(List<StockSaleQtyBatchEntity> saleQtyBatchList) {

        return saleQtyBatchList.stream().collect(Collectors.groupingBy(StockSaleQtyBatchEntity::getSkuId));
    }

    public Map<BigInteger, List<SafeSaleQtyProductConfigEntity>> buildSkuSafeQtyMap(BigInteger profileId, List<BigInteger> skuIdList) {
        boolean useConfig = GlobalConfig.get(StringConstant.PRODUCT_SAFE_QTY_SYS_DATA_KEY).equals(StringConstant.SYS_DATA_SUCCESS);
        if (!useConfig) {
            return null;
        }
        if (CollectionUtils.isEmpty(skuIdList)) {
            return null;
        }
        List<SafeSaleQtyProductConfigEntity> skuSafeQtyList = setSafeQtyConfigSvc.queryProductSafeQtyConfigList(profileId, skuIdList);
        if (CollectionUtils.isEmpty(skuSafeQtyList)) {
            return null;
        }
        return skuSafeQtyList.stream().collect(Collectors.groupingBy(SafeSaleQtyProductConfigEntity::getKtypeId));
    }

    private StockSyncRule queryRuleByMapping(EshopProductSkuMapping mapping, List<StockSyncRule> syncRules) {
        BigInteger ruleId = mapping.getSyncRuleId();
        Optional<StockSyncRule> first = syncRules.stream().filter(x -> x.getId().equals(ruleId)).findFirst();
        if (first.isPresent()) {
            return first.get();
        }
        Optional<StockSyncRule> defaultRule = syncRules.stream().filter(StockSyncRuleBase::isDefaultRule).findFirst();
        if (defaultRule.isPresent()) {
            return defaultRule.get();
        }
        throw new RuntimeException("未找到默认库存规则，请联系管家婆技术处理！");
    }

    private List<StockSyncRule> queryRuleListByMapping(EshopInfo eshopInfo, List<EshopProductSkuMapping> mappingList) {
        List<BigInteger> ruleIds = mappingList.stream().map(EshopProductSkuMapping::getSyncRuleId).collect(Collectors.toList());
        QueryStockRuleParameter parameter = new QueryStockRuleParameter();
        parameter.setEshopId(eshopInfo.getOtypeId());
        parameter.setTargetType(StockRuleTargetTypeEnum.NORMAL);
        parameter.setContainsInitRule(true);
        parameter.setRuleIds(ruleIds);
        return ruleSvc.queryRulesContainsInitRule(parameter);
    }

    public List<BigInteger> getSkuIdListByMapping(List<EshopProductSkuMapping> mappingList) {
        List<BigInteger> allSkuIdList = new ArrayList<>();
        if (CollectionUtils.isEmpty(mappingList)) {
            return allSkuIdList;
        }
        List<BigInteger> comboIds = mappingList.stream().filter(x -> x.getPcategory().equals(Pcategory.Combo)).map(EshopProductSkuMapping::getPtypeId).collect(Collectors.toList());
        List<ComboDetail> comboDetails = new ArrayList<>();
        if (!comboIds.isEmpty()) {
            if (comboIds.size() > 500) {
                List<List<BigInteger>> comboIdsList = SaleUtil.splitList(comboIds, 500);
                for (List<BigInteger> comboIdList : comboIdsList) {
                    comboDetails.addAll(queryComboDetails(comboIdList));
                }
            } else {
                comboDetails = queryComboDetails(comboIds);
            }
        }

        for (EshopProductSkuMapping mapping : mappingList) {
            if (!mapping.getPcategory().equals(Pcategory.Combo)) {
                allSkuIdList.add(mapping.getSkuId());
                continue;
            }
            List<ComboDetail> currentMsgComboDetails = comboDetails.stream().filter(x -> x.getComboId().equals(mapping.getPtypeId())).collect(Collectors.toList());
            mapping.setComboDetailList(currentMsgComboDetails);
        }

        List<ComboDetail> details = comboDetails.stream().filter(ComboDetail::getNecessarySku).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(details)) {
            List<BigInteger> skuIds = details.stream().map(ComboDetail::getSkuId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(skuIds)) {
                allSkuIdList.addAll(skuIds);
            }
        }

        return allSkuIdList.stream().distinct().collect(Collectors.toList());
    }


    public List<BigInteger> getKtypeIdsByProfileId() {
        List<Stock> stockList = baseSvc.getStockList();
        if (CollectionUtils.isEmpty(stockList)) {
            return new ArrayList<>();
        }
        return stockList.stream().map(Stock::getId).collect(Collectors.toList());
    }

    public List<ComboDetail> queryComboDetailsById(BigInteger comboId) {
        return baseSvc.getComboDetailsById(comboId);
    }

    public List<ComboDetail> queryComboDetails(List<BigInteger> comboIds) {
        if (CollectionUtils.isEmpty(comboIds)) {
            return new ArrayList<>();
        }
        QueryComboParameter parameter = new QueryComboParameter();
        parameter.setPtypeIdList(comboIds);
        return baseSvc.getComboDetails(parameter);
    }

    public boolean GetIsOpenOneStockTakingConfig() {
        String sysCon = GlobalConfig.get(StringConstant.OPEN_ONE_STOCK_TAKING, "0");
        //先检查账套级别配置是否强制关闭，如果强制关闭直接返回关闭结果
        if (sysCon.equals(StringConstant.SYS_DATA_CLOSE)) {
            return false;
        }
        //再检查部署级别配置是否开启，开启配置后直接返回开启结果
        if (bizConfig.isOpenOneStockTaking()) {
            return true;
        }
        //最后再检查账套级别配置是否开启，开启则结果开启，未开启则结果关闭
        return sysCon.equals(StringConstant.SYS_DATA_SUCCESS);
    }

    /*
     * 构建网店一盘货库存信息
     * */
    public void buildEshopOneStockTaking(List<StockSaleQtyEntity> qtyList, Otype otype) {
        if (ngp.utils.CollectionUtils.isEmpty(qtyList)) {
            return;
        }
        if (!bizConfig.getOpenOneStockTakingEshopType().contains(otype.getShopType().getCode())) {
            return;
        }
        for (StockSaleQtyEntity itemSku : qtyList) {
            List<StockOrderDetailEntity> orderDetails = itemSku.getOrderDetails();
            if (ngp.utils.CollectionUtils.isEmpty(orderDetails)) {
                continue;
            }
            logger.error("构建网店一盘货库存信息:stockQty：{} recordQty：{} shopOrderList：{}", itemSku.getStockQty(), itemSku.getRecordQty(),
                    JsonUtils.toJson(orderDetails));
            BigDecimal shopOrderQty = orderDetails.stream().filter(orderDetail ->
                            itemSku.getKtypeId().compareTo(orderDetail.getKtypeId()) == 0 && orderDetail.getOtypeId().equals(otype.getId()))
                    .map(StockOrderDetailEntity::getShopOrderQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            itemSku.setRecordQty(itemSku.getRecordQty().subtract(shopOrderQty));
        }
    }

    /*
     * 构建网店一盘货批次库存信息
     * */
    public void buildBatchEshopOneStockTaking(List<StockSaleQtyBatchEntity> qtyList, Otype otype) {
        if (ngp.utils.CollectionUtils.isEmpty(qtyList)) {
            return;
        }
        if (!bizConfig.getOpenOneStockTakingEshopType().contains(otype.getShopType().getCode())) {
            return;
        }
        for (StockSaleQtyBatchEntity itemSku : qtyList) {
            List<StockOrderDetailEntity> orderDetails = itemSku.getOrderDetails();
            if (ngp.utils.CollectionUtils.isEmpty(orderDetails)) {
                continue;
            }
            BigDecimal shopOrderQty = orderDetails.stream().filter(orderDetail -> itemSku.getKtypeId().compareTo(orderDetail.getKtypeId()) == 0
                            && orderDetail.getOtypeId().equals(otype.getId()))
                    .map(StockOrderDetailEntity::getShopOrderQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            itemSku.setRecordQty(itemSku.getRecordQty().subtract(shopOrderQty));
        }
    }

    public List<StockSaleQtyEntity> getSaleQtyBySkuIds(List<BigInteger> skuIds, List<BigInteger> ktypeIds) {
        try {
            if (CollectionUtils.isEmpty(skuIds)) {
                return new ArrayList<>();
            }
            List<BigInteger> distinctSku = skuIds.stream().distinct().collect(Collectors.toList());
            QueryStockParameter parameter = new QueryStockParameter();
            parameter.setProfileId(CurrentUser.getProfileId());
            parameter.setKtypeIdList(ktypeIds);
            parameter.setSkuIdList(distinctSku);
            parameter.setNeedQueryLockRecord(false);
            return qtyService.querySaleQtyList(parameter);
        } catch (Exception e) {
            throw new RuntimeException(String.format("查询库存可销售库存报错【%s】", e.getMessage()), e);
        }
    }

    public List<StockSendQtyEntity> getSendQtyBySkuIds(List<BigInteger> skuIds, List<BigInteger> ktypeIds) {
        try {
            if (CollectionUtils.isEmpty(skuIds)) {
                return new ArrayList<>();
            }
            List<BigInteger> distinctSku = skuIds.stream().distinct().collect(Collectors.toList());
            QueryStockParameter parameter = new QueryStockParameter();
            parameter.setProfileId(CurrentUser.getProfileId());
            parameter.setKtypeIdList(ktypeIds);
            parameter.setSkuIdList(distinctSku);
            return qtyService.querySendQtyList(parameter);
        } catch (Exception e) {
            throw new RuntimeException(String.format("查询库存可发货库存报错【%s】", e.getMessage()), e);
        }
    }

    public List<ComboDetail> queryComboDetails(List<BigInteger> comboIds, boolean needVirtual) {
        if (CollectionUtils.isEmpty(comboIds)) {
            return new ArrayList<>();
        }
        QueryComboParameter parameter = new QueryComboParameter();
        parameter.setPtypeIdList(comboIds);
        parameter.setNeedVirtual(needVirtual);
        return baseSvc.getComboDetails(parameter);
    }

    public List<EshopProductSkuPageData> queryStockByPageData(@RequestBody List<EshopProductSkuPageData> pageDataList, boolean needMultiStock) {
        if (CollectionUtils.isEmpty(pageDataList)) {
            return pageDataList;
        }
        BigInteger profileId = CurrentUser.getProfileId();
        Map<BigInteger, List<StockSyncRule>> defaultRules = getAutoSyncDefaultRules(pageDataList, profileId, needMultiStock);
        Map<BigInteger, List<StockSyncRule>> syncRules = getRuleListByRuleId(pageDataList, profileId, needMultiStock);
        Map<BigInteger, List<ComboDetail>> comboMap = getComboDetailMap(pageDataList);
        Map<BigInteger, List<StockSaleQtyEntity>> skuSaleQtys = getSaleQtyListMap(pageDataList, comboMap);
        BuildPageDateMultiStockList(pageDataList,needMultiStock);
        for (EshopProductSkuPageData data : pageDataList) {
            StockSyncRule rule = defaultRules.get(data.getOtypeId()).get(0);
            if (data.getSyncRuleId() != null && data.getSyncRuleId().compareTo(BigInteger.ZERO) != 0 && syncRules.containsKey(data.getSyncRuleId())) {
                rule = syncRules.get(data.getSyncRuleId()).get(0);
            }
            data.setKtypeNameList(rule.getKtypeNames());
            data.setFormula(rule.getFormula());
            BigDecimal syncQty = BuildProductSkuPageSyncQtyData(skuSaleQtys, comboMap, data, rule);
            if (!data.getQtyHasModified())
            {
                data.setQty(syncQty);
                data.setSaleQty(syncQty);
            }
            if (needMultiStock)
            {
                BuildWareHouseRuleList(skuSaleQtys, comboMap, data, rule);
                BuildMultiStockSyncList(skuSaleQtys, comboMap, data, rule);
            }
        }
        return pageDataList;
    }

    private BigDecimal BuildProductSkuPageSyncQtyData(Map<BigInteger, List<StockSaleQtyEntity>> skuSaleQtys,
                                                      Map<BigInteger, List<ComboDetail>> comboMap, EshopProductSkuPageData data, StockSyncRule rule) {
        if (data.getPcategory() != null && data.getPcategory().equals(Pcategory.Combo)) {
            if (data.getPtypeId() == null || !comboMap.containsKey(data.getPtypeId())) {
                return BigDecimal.valueOf(0);
            }
            StockCalByComboParameter comboParameter = new StockCalByComboParameter();
            comboParameter.setRule(rule);
            List<ComboDetail> details = comboMap.get(data.getPtypeId());
            comboParameter.setCombos(details);
            comboParameter.setEshopId(data.getOtypeId());
            comboParameter.setProfileId(CurrentUser.getProfileId());
            List<StockSaleQtyEntity> comboQtyList = new ArrayList<>();
            for (ComboDetail detail : details) {
                if (!skuSaleQtys.containsKey(detail.getSkuId())) {
                    return BigDecimal.valueOf(0);
                }
                comboQtyList.addAll(skuSaleQtys.get(detail.getSkuId()));
            }
            comboParameter.setSaleQtyList(comboQtyList);
            BigDecimal qtyByCombo = getSyncQtyByCombo(comboParameter);
            return qtyByCombo;
        }
        if (data.getSkuId() == null || !skuSaleQtys.containsKey(data.getSkuId())) {
            return BigDecimal.valueOf(0);
        }
        StockCalParameter parameter = new StockCalParameter();
        parameter.setEshopId(data.getOtypeId());
        parameter.setRule(rule);
        parameter.setSaleQtyList(skuSaleQtys.get(data.getSkuId()));
        BigDecimal syncQty = getSyncQty(data.getSkuId(), parameter);
        return syncQty;
    }

    private void BuildWareHouseRuleList(Map<BigInteger, List<StockSaleQtyEntity>> skuSaleQtys,
                                        Map<BigInteger, List<ComboDetail>> comboMap,
                                        EshopProductSkuPageData data, StockSyncRule rule) {
        if (!rule.getWarehouseStockSyncEnabled() || CollectionUtils.isEmpty(rule.getStoreRuleLsit())) {
            return;
        }
        List<StockSyncRule> eshopRules = rule.getStoreRuleLsit().stream().
                filter(x -> x.getOtypeId().compareTo(data.getOtypeId()) == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(eshopRules) || CollectionUtils.isNotEmpty(data.getWareHouseStocks())) {
            return;
        }
        List<WareHouseStockSync> wareHouseStocks = new ArrayList<>();
        for (StockSyncRule item : eshopRules) {
            WareHouseStockSync itemWareHouse = new WareHouseStockSync();
            itemWareHouse.setWarehouseCode(item.getWarehouseCode());
            itemWareHouse.setWarehouseName(item.getWarehouseName());
            itemWareHouse.setWarehouseType(item.getWarehouseType());
            BigDecimal syncQty = BuildProductSkuPageSyncQtyData(skuSaleQtys, comboMap, data, item);
            itemWareHouse.setSyncQty(syncQty.longValue());
            wareHouseStocks.add(itemWareHouse);
        }
        data.setWareHouseStocks(wareHouseStocks);
    }

    private void BuildMultiStockSyncList(Map<BigInteger, List<StockSaleQtyEntity>> skuSaleQtys,
                                         Map<BigInteger, List<ComboDetail>> comboMap,
                                         EshopProductSkuPageData data, StockSyncRule rule) {
        if (!rule.getEshopMultiStockSyncEnabled() || CollectionUtils.isEmpty(rule.getMultiStockSyncDetailList())
                || CollectionUtils.isEmpty(data.getMultiTimeStocks())) {
            return;
        }
        Map<String, List<MultiStockSyncDetail>> multStockMaps = rule.getMultiStockSyncDetailList().stream().collect(Collectors.groupingBy(MultiStockSyncDetail::getPlatformMultiId));
        List<MultiTimeStock> times = new ArrayList<>();
        for (MultiTimeStock dataTimeStocks : data.getMultiTimeStocks()) {
            if (!multStockMaps.containsKey(dataTimeStocks.getTimeType().toString())) {
                continue;
            }
            MultiTimeStock item = new MultiTimeStock();
            item.setTimeType(dataTimeStocks.getTimeType());
            item.setTimeDesc(dataTimeStocks.getTimeDesc());
            MultiStockSyncDetail syncTimeDetail = multStockMaps.get(dataTimeStocks.getTimeType().toString()).get(0);
            if (syncTimeDetail.getSyncType().equals(StockRuleSyncType.FORMULA)) {
                StockSyncRule timeRule = JsonUtils.toObject(JsonUtils.toJson(rule), StockSyncRule.class);
                timeRule.setRuleType(StockRuleType.SharedInventory);
                timeRule.setRuleCron(syncTimeDetail.getSyncCron());
                BigDecimal syncQty = BuildProductSkuPageSyncQtyData(skuSaleQtys, comboMap, data, timeRule);
                item.setStockNum(syncQty.longValue());
            }
            if (syncTimeDetail.getSyncType().equals(StockRuleSyncType.FIX_QTY)) {
                item.setStockNum(syncTimeDetail.getFixQty().longValue());
            }
            times.add(item);
        }
        data.setMultiTimeStocks(times);
    }

    private Map<BigInteger, List<StockSyncRule>> getAutoSyncDefaultRules(List<EshopProductSkuPageData> pageDataList, BigInteger profileId, boolean needMultiStock) {
        if (ngp.utils.CollectionUtils.isEmpty(pageDataList)) {
            return new HashMap<>();
        }
        Map<BigInteger, List<StockSyncRule>> resultMap = new HashMap<>();
                List<BigInteger> eshopIds = pageDataList.stream().map(x -> x.getOtypeId()).distinct().collect(Collectors.toList());
        ;
        List<StockSyncRule> defaultRules = ruleSvc.getAutoSyncDefaultRulesByEshop(profileId, eshopIds,false);
        if (needMultiStock) {
            buildRuleStockSyncDetails(defaultRules, eshopIds);
        }
        if (ngp.utils.CollectionUtils.isNotEmpty(defaultRules)) {
            resultMap = defaultRules.stream().filter(x->x.getTargetType().equals(StockRuleTargetTypeEnum.NORMAL)).collect(Collectors.groupingBy(StockSyncRule::getId));
        }
        List<Stock> stockList = resultMap.size() < eshopIds.size() ? ruleSvc.getBaseInfoKtypes() : new ArrayList<>();
        List<BigInteger> ktypeIds = stockList.stream().map(Stock::getId).collect(Collectors.toList());
        for (BigInteger eshopid : eshopIds) {
            if (resultMap.containsKey(eshopid)) {
                continue;
            }
            StockSyncRule rule = new StockSyncRule();
            rule.setRuleType(StockRuleType.SharedInventory);
            rule.setDefaultRule(true);
            rule.setRuleName("【默认规则】店铺同步规则");
            rule.setKtypeDataSource(stockList);
            rule.setKtypeIds(StringUtils.join(ktypeIds));
            rule.setStockRuleDetailList(ruleSvc.buildDefaultRuleDetails(rule, stockList));
            rule.setEshopMultiStockSyncEnabled(false);
            rule.setWarehouseStockSyncEnabled(false);
            rule.setAutoUpShelfEnabled(false);
            resultMap.put(eshopid, Arrays.asList(rule));
        }
        return resultMap;
    }

    private Map<BigInteger, List<StockSyncRule>> getRuleListByRuleId(List<EshopProductSkuPageData> pageDataList, BigInteger profileId, boolean needMultiStock) {
        if (ngp.utils.CollectionUtils.isEmpty(pageDataList)) {
            return new HashMap<>();
        }
        List<BigInteger> syncRuleIds = pageDataList.stream()
                .filter(m -> m.getSyncRuleId() != null && m.getSyncRuleId().compareTo(BigInteger.ZERO) != 0)
                .map(x -> x.getSyncRuleId()).collect(Collectors.toList());
        QueryStockRuleParameter parameter = new QueryStockRuleParameter();
        parameter.setRuleIds(syncRuleIds);
        parameter.setProfileId(profileId);
        List<StockSyncRule> ruleList = ruleSvc.queryRuleWithSyncRuleIds(parameter);
        if (ngp.utils.CollectionUtils.isEmpty(ruleList)) {
            return new HashMap<>();
        }
        if (needMultiStock) {
            List<BigInteger> eshopIds = pageDataList.stream().map(x -> x.getOtypeId()).distinct().collect(Collectors.toList());
            buildRuleStockSyncDetails(ruleList, eshopIds);
        }
        return ruleList.stream().collect(Collectors.groupingBy(StockSyncRule::getId));
    }

    private Map<BigInteger, List<StockSaleQtyEntity>> getSaleQtyListMap(List<EshopProductSkuPageData> pageDataList,
                                                                        Map<BigInteger, List<ComboDetail>> comboMap) {
        if (ngp.utils.CollectionUtils.isEmpty(pageDataList)) {
            return new HashMap<>();
        }
        List<BigInteger> skuIds = pageDataList.stream().map(x -> x.getSkuId()).distinct().collect(Collectors.toList());
        if (comboMap != null && comboMap.size() > 0) {
            List<ComboDetail> allComboDetails = comboMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
            List<BigInteger> comboSkuIds = allComboDetails.stream().map(ComboDetail::getSkuId).distinct().collect(Collectors.toList());
            if (ngp.utils.CollectionUtils.isNotEmpty(comboSkuIds)) {
                skuIds.addAll(comboSkuIds);
            }
        }
        List<StockSaleQtyEntity> saleQtyList = getSaleQtyList(skuIds);
        if (ngp.utils.CollectionUtils.isEmpty(saleQtyList)) {
            return new HashMap<>();
        }
        return saleQtyList.stream().collect(Collectors.groupingBy(StockSaleQtyEntity::getSkuId));
    }

    private Map<BigInteger, List<ComboDetail>> getComboDetailMap(List<EshopProductSkuPageData> pageDataList) {
        if (ngp.utils.CollectionUtils.isEmpty(pageDataList)) {
            return new HashMap<>();
        }
        List<BigInteger> comboIds = pageDataList.stream().filter(m -> m.getPcategory() != null && m.getPcategory().equals(Pcategory.Combo)).map(x -> x.getPtypeId()).collect(Collectors.toList());
        if (ngp.utils.CollectionUtils.isEmpty(comboIds)) {
            return new HashMap<>();
        }
        List<ComboDetail> comboDetails = new ArrayList<>();
        if (!comboIds.isEmpty()) {
            if (comboIds.size() > 500) {
                List<List<BigInteger>> comboIdsList = SaleUtil.splitList(comboIds, 500);
                for (List<BigInteger> comboIdList : comboIdsList) {
                    comboDetails.addAll(queryComboDetails(comboIdList));
                }
            } else {
                comboDetails = queryComboDetails(comboIds);
            }
        }
        if (ngp.utils.CollectionUtils.isEmpty(comboDetails)) {
            return new HashMap<>();
        }
        return comboDetails.stream().collect(Collectors.groupingBy(ComboDetail::getComboId));
    }

    public void buildRuleStockSyncDetails(List<StockSyncRule> ruleList, List<BigInteger> eshopIds) {
        ruleSvc.buildRuleMultiStockSyncDetails(ruleList);
        ruleSvc.buildWarehouseStockSyncDetails(ruleList, eshopIds);
    }

    public void BuildPageDateMultiStockList(List<EshopProductSkuPageData> pageDataList,boolean needMultiStock) {
        if (!needMultiStock || CollectionUtils.isEmpty(pageDataList))
        {
            return;
        }
        List<String> uniqueIds = pageDataList.stream().map(EshopProductSkuPageData::getUniqueId).distinct().collect(Collectors.toList());
        List<BigInteger> eshopIds = pageDataList.stream().map(EshopProductSkuPageData::getOtypeId).distinct().collect(Collectors.toList());
        Map<String, List<MultiTimeStock>> timeMapList = getMultiTimeStockMap(uniqueIds,eshopIds);
        for (EshopProductSkuPageData item : pageDataList) {
            if (!timeMapList.containsKey(item.getUniqueId()))
            {
                continue;
            }
            if (CollectionUtils.isNotEmpty(item.getMultiTimeStocks()))
            {
                continue;
            }
            item.setMultiTimeStocks(timeMapList.get(item.getUniqueId()));
        }
    }

    public Map<String, List<MultiTimeStock>> getMultiTimeStockMap(List<String> uniqueIds, List<BigInteger> eshopIds) {
        Map<String, List<MultiTimeStock>> timeMapList = new HashMap<>();
        if (ngp.utils.CollectionUtils.isEmpty(uniqueIds)) {
            return timeMapList;
        }
        QueryProductMarkRequest parameter = new QueryProductMarkRequest();
        parameter.setProfileId(CurrentUser.getProfileId());
        parameter.setEshopIds(eshopIds);
        parameter.setUniqueIds(uniqueIds);
        List<EshopProductMark> productMarkList = ruleSvc.QueyProductTimingMark(parameter);
        if (ngp.utils.CollectionUtils.isEmpty(productMarkList))
        {
            return timeMapList;
        }
        for (EshopProductMark data : productMarkList) {
            List<MultiTimeStock> timestocks = JsonUtils.toList(data.getProductMarkBigData(), MultiTimeStock.class);
            if (CollectionUtils.isEmpty(timestocks) || timeMapList.containsKey(data.getUniqueId()))
            {
                continue;
            }
            timeMapList.put(data.getUniqueId(),timestocks);
        }
        return timeMapList;

    }


    public Map<BigInteger, List<StockSaleQtyBatchEntity>> getSaleQtyBatchListMap(List<EshopProductSkuPageData> pageDataList,
                                                                        boolean openOneStockTaking) {
        if (ngp.utils.CollectionUtils.isEmpty(pageDataList)) {
            return new HashMap<>();
        }
        List<BigInteger> skuIds = pageDataList.stream().map(x -> x.getSkuId()).distinct().collect(Collectors.toList());
        //先把所有批次查询出现，然后使用skuId作为key，方便以后获取，提高性能
        QueryStockParameter queryBatchParam = new QueryStockParameter();
        queryBatchParam.setProfileId(CurrentUser.getProfileId());
        queryBatchParam.setSkuIdList(skuIds);
        queryBatchParam.setNeedStockDetail(openOneStockTaking);
        queryBatchParam.setNeedQueryLockRecord(false);
        queryBatchParam.setCheckStockLimited(false);
        List<StockSaleQtyBatchEntity> batchSaleQtyList =  qtyService.queryBatchSaleQtyList(queryBatchParam) ;
        if (ngp.utils.CollectionUtils.isEmpty(batchSaleQtyList)) {
            return new HashMap<>();
        }
        return batchSaleQtyList.stream().collect(Collectors.groupingBy(StockSaleQtyBatchEntity::getSkuId));
    }
    public List<QueryEshopSqlSkuData> queryEshopSaleStockBySkuids(QueryEshopSqlSkuParam queryParam) {
        List<QueryEshopSqlSkuData> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(queryParam.getSkuIdList())) {
            return resultList;
        }
        BigInteger profileId = CurrentUser.getProfileId();
        StockSyncRule rule = null;
        BigInteger eshopId = queryParam.getEshopId();
        if (queryParam.getEshopId() != null && queryParam.getEshopId().compareTo(BigInteger.ZERO) != 0)
        {
            List<StockSyncRule> defaultRules = ruleSvc.getAutoSyncDefaultRulesByEshop(profileId, Arrays.asList(eshopId),false);
            if (CollectionUtils.isNotEmpty(defaultRules))
            {
                rule = defaultRules.get(0);
            }
        }
        List<BigInteger> skuIds = queryParam.getSkuIdList().stream().distinct().collect(Collectors.toList());
        List<StockSaleQtyEntity> saleQtyList = getSaleQtyList(skuIds);
        if (CollectionUtils.isEmpty(saleQtyList))
        {
            return resultList;
        }
        Map<BigInteger,List<StockSaleQtyEntity>> saleQtyMap = saleQtyList.stream().collect(Collectors.groupingBy(StockSaleQtyEntity::getSkuId));
        for (Map.Entry<BigInteger, List<StockSaleQtyEntity>> entry : saleQtyMap.entrySet()) {
            List<StockSaleQtyEntity> itemSkuSaleList = entry.getValue();
            BigInteger skuId =  entry.getKey();
            QueryEshopSqlSkuData itemResult = new QueryEshopSqlSkuData();
            itemResult.setSkuId(skuId);
            if (rule == null)
            {
                BigDecimal qty = itemSkuSaleList.stream().map(StockSaleQtyEntity::getSaleQty).collect(Collectors.reducing(BigDecimal.ZERO,BigDecimal::add));
                itemResult.setQty(qty);
                resultList.add(itemResult);
                continue;
            }
            StockCalParameter parameter = new StockCalParameter();
            parameter.setEshopId(eshopId);
            parameter.setRule(rule);
            parameter.setSaleQtyList(itemSkuSaleList);
            BigDecimal qty = getSyncQty(skuId, parameter);
            itemResult.setQty(qty);
            resultList.add(itemResult);
        }
        return resultList;
    }
}
