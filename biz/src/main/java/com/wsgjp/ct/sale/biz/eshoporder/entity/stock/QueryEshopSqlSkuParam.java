package com.wsgjp.ct.sale.biz.eshoporder.entity.stock;

import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark;

import java.math.BigInteger;
import java.util.List;

/**
 * @Description: 查询网店sku可销售库存请求参数
 * <AUTHOR> zxpeng
 * @Date: 2025-07-31 18:19
 */
public class QueryEshopSqlSkuParam {
    private List<BigInteger> skuIdList;
    private BigInteger eshopId;

    public List<BigInteger> getSkuIdList() {
        return skuIdList;
    }

    public void setSkuIdList(List<BigInteger> skuIdList) {
        this.skuIdList = skuIdList;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }
}
